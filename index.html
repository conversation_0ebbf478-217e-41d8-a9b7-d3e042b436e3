<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mechanical Engineering Terms Reviewer</title>
  <link rel="stylesheet" href="style.css">
  <!-- Include Chart.js via CDN -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

  <!-- Favicon -->
  <link rel="apple-touch-icon" sizes="180x180" href="favicon/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="favicon/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="favicon/favicon-16x16.png">
  <link rel="shortcut icon" href="favicon/favicon.ico">
  <link rel="manifest" href="favicon/site.webmanifest">
  <link rel="mask-icon" href="favicon/safari-pinned-tab.svg" color="#5bbad5">
  <meta name="msapplication-TileColor" content="#2b5797">
  <meta name="msapplication-config" content="favicon/browserconfig.xml">
  <meta name="theme-color" content="#ffffff">
</head>
<body>
  <div class="quiz-container">
    <div id="start-screen">
      <h1 id="game-title">Mechanical Engineering Terms Reviewer</h1>
      <div class="last-updated-container">
        <div id="last-updated"></div>
      </div>
      <div id="quick-start-guide">
        <p><strong>Quick Start Guide:</strong></p>
        <ul>
          <li>Select a quiz from the dropdown.</li>
          <li>Choose Practice, Quiz, or Learn Mode. (I suggest Learn Mode first)</li>
          <li>Pick Ordered (based on PDF) or Random question order.</li>
          <li>Click "Start" to begin!</li>
        </ul>
      </div>
      <select id="quizSelect">
        <option value="">Select a Quiz</option>
      </select>
      <div>
        <label><input type="radio" name="mode" value="practice" checked> Practice Mode</label>
        <label><input type="radio" name="mode" value="quiz"> Quiz Mode</label>
        <label><input type="radio" name="mode" value="learn"> Learn Mode</label>
      </div>
      <div>
        <label><input type="radio" name="order" value="ordered" checked> Ordered</label>
        <label><input type="radio" name="order" value="random"> Random</label>
      </div>
      <div id="settings">
        <label><input type="checkbox" id="repeatsEnabled" name="repeatsEnabled"> Enable Repeats</label>
        <label>Max Repeats: <input type="number" id="maxRepeatsInput" name="maxRepeats" min="0" max="5" value="1"></label>
        <!-- Speed Run options, only shown in Quiz Mode -->
        <div id="speedRunContainer" style="display: none;">
          <label><input type="checkbox" id="speedRunEnabled" name="speedRunEnabled"> Enable Speed Run</label>
          <label>Time Limit: <input type="text" id="speedRunTimeInput" name="speedRunTime" value="30s"></label>
        </div>
        <!-- Reveal Correct Answer option, only shown in Quiz Mode -->
        <div id="revealAnswerContainer" style="display: none;">
          <label><input type="checkbox" id="revealCorrectAnswer" name="revealCorrectAnswer"> Reveal Correct Answer after Incorrect Guess</label>
        </div>
      </div>
      <button id="startButton">Start Practice</button>
      <!-- New button to see quiz data -->
      <button id="seeQuizDataButton">See Quiz Data</button>
    </div>
    <div id="game-screen" class="hidden">
      <button id="quitButton">X</button>
      <button id="infoButton">💪</button>
      <div id="question" class="question"></div>
      <div id="answer" class="answer"></div>
      <div id="navigation">
        <button id="prevButton">← Previous</button>
        <button id="nextButton">Next →</button>
      </div>
      <div id="choices" class="choices"></div>
      <button id="skipButton">Skip</button>
      <!-- End Quiz Early button (only visible in Quiz Mode) -->
      <button id="endEarlyButton">End Quiz Early</button>
      <div id="progress"></div>
      <div id="feedback" class="feedback"></div>
      <div id="quoteDisplay" class="quote-display hidden">
        <p id="quoteText"></p>
        <button id="closeQuote">Close</button>
      </div>
      <button id="creditsButton">Credits</button>
      <div id="creditsMessage" class="hidden">
        Created by JMLC using ChatGPT and Grok 3 for Power Plant Engineering Review of terms.<br>
        Review questions are from Engr. Alfred Olasiman and Dr. Tony Doroliat.<br>
        Special Thanks to AZ for introducing Netlify.<br>
        <a href="favicon/favicon-attribution.html" target="_blank">Favicon Attribution</a>
      </div>
    </div>
    <!-- New chart screen to display the graph -->
    <div id="chart-screen" class="hidden">
      <h2>Quiz Accuracy Over Time</h2>
      <canvas id="accuracyChart"></canvas>
      <button id="closeChart">Close</button>
    </div>
  </div>
  <audio id="correctSound" src="/sounds/correct.mp3"></audio>
  <audio id="incorrectSound" src="/sounds/incorrect.mp3"></audio>
  <script>const __BUILD_DATE__ = "Tuesday, March 4, 2025 at 05:31:07 PM GMT+8";</script>
  <script src="script.js"></script>
  <script>
    document.querySelectorAll('input[name="mode"]').forEach((input) => {
      input.addEventListener("change", (e) => {
        const settings = document.getElementById("settings");
        const speedRunContainer = document.getElementById("speedRunContainer");
        const revealAnswerContainer = document.getElementById("revealAnswerContainer");
        if (e.target.value === "learn") {
          settings.style.display = "none";
          speedRunContainer.style.display = "none";
          revealAnswerContainer.style.display = "none";
        } else {
          settings.style.display = "block";
          // Only display Speed Run and Reveal Answer options in Quiz Mode
          if (e.target.value === "quiz") {
            speedRunContainer.style.display = "block";
            revealAnswerContainer.style.display = "block";
          } else {
            speedRunContainer.style.display = "none";
            revealAnswerContainer.style.display = "none";
          }
        }
        mode = e.target.value;
        document.getElementById("startButton").textContent = `Start ${mode.charAt(0).toUpperCase() + mode.slice(1)}`;
      });
    });
  </script>
</body>
</html>