{"name": "chart.js", "homepage": "https://www.chartjs.org", "description": "Simple HTML5 charts using the canvas element.", "version": "4.4.8", "license": "MIT", "type": "module", "sideEffects": ["./auto/auto.js", "./auto/auto.cjs", "./dist/chart.umd.js"], "jsdelivr": "./dist/chart.umd.js", "unpkg": "./dist/chart.umd.js", "main": "./dist/chart.cjs", "module": "./dist/chart.js", "exports": {".": {"types": "./dist/types.d.ts", "import": "./dist/chart.js", "require": "./dist/chart.cjs"}, "./auto": {"types": "./auto/auto.d.ts", "import": "./auto/auto.js", "require": "./auto/auto.cjs"}, "./helpers": {"types": "./helpers/helpers.d.ts", "import": "./helpers/helpers.js", "require": "./helpers/helpers.cjs"}}, "types": "./dist/types.d.ts", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "repository": {"type": "git", "url": "https://github.com/chartjs/Chart.js.git"}, "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "files": ["auto/**", "dist/**", "!dist/docs/**", "helpers/**"], "scripts": {"autobuild": "rollup -c -w", "copyDeclarations": "node -e \"fs.cpSync('./src/types/', './dist/types/', {recursive:true})\"", "emitDeclarations": "tsc --emitDeclarationOnly && pnpm copyDeclarations", "build": "rollup -c && pnpm emitDeclarations", "dev": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers chrome --grep", "dev:ff": "karma start ./karma.conf.cjs --auto-watch --no-single-run --browsers firefox --grep", "docs": "pnpm run build && pnpm --filter \"./docs/**\" build", "docs:dev": "pnpm run build && pnpm --filter \"./docs/**\" dev", "lint-js": "eslint \"src/**/*.{js,ts}\" \"test/**/*.js\" \"docs/**/*.js\" --cache", "lint-md": "eslint \"**/*.md\" --cache", "lint-types": "pnpm build && node test/types/autogen.js && tsc -p test/types", "lint": "concurrently \"pnpm:lint-*\"", "test": "pnpm lint && pnpm test-ci", "test-ci": "concurrently \"pnpm:test-ci-*\"", "test-ci-karma": "cross-env NODE_ENV=test karma start ./karma.conf.cjs --auto-watch --single-run --coverage --grep", "test-ci-integration": "pnpm --filter \"./test/integration/**\" test"}, "dependencies": {"@kurkle/color": "^0.3.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^23.0.2", "@rollup/plugin-inject": "^5.0.2", "@rollup/plugin-json": "^5.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@swc/core": "^1.3.18", "@types/estree": "^1.0.0", "@types/offscreencanvas": "^2019.7.0", "@typescript-eslint/eslint-plugin": "^5.32.0", "@typescript-eslint/parser": "^5.32.0", "chartjs-adapter-luxon": "^1.2.0", "chartjs-adapter-moment": "^1.0.0", "chartjs-test-utils": "^0.4.0", "concurrently": "^7.3.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "eslint": "^8.21.0", "eslint-config-chartjs": "^0.3.0", "eslint-plugin-es": "^4.1.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-markdown": "^3.0.0", "esm": "^3.2.25", "glob": "^8.0.3", "jasmine": "^3.7.0", "jasmine-core": "^3.7.1", "karma": "^6.3.2", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.3", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^2.1.0", "karma-jasmine": "^4.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-rollup-preprocessor": "7.0.7", "karma-safari-private-launcher": "^1.0.0", "karma-spec-reporter": "0.0.32", "luxon": "^3.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.34", "pixelmatch": "^5.3.0", "rollup": "^3.3.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-istanbul": "^4.0.0", "rollup-plugin-swc3": "^0.7.0", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.7.4", "yargs": "^17.5.1"}, "engines": {"pnpm": ">=8"}, "packageManager": "pnpm@8.13.0", "pnpm": {"overrides": {"html-entities": "1.4.0"}, "peerDependencyRules": {"ignoreMissing": ["chart.js"]}}}