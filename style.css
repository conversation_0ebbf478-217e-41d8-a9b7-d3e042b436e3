/* General body styling - Dark theme */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    text-align: center;
    margin: 10px;
    background-color: #000000;
    color: #ffffff;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Main container */
  .quiz-container {
    width: 90%;
    max-width: 620px;
    min-width: 300px;
    margin: 0 auto;
    background: rgba(12, 12, 12, 0.788);
    padding: 18px;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(241, 241, 241, 0.719), inset 0 0 10px rgba(0, 170, 255, 0.1);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }
  
  /* Game title */
  #game-title {
    font-size: 28px;
    text-shadow: 0 1px 5px #8f8f8f;
    font-weight: 700;
    margin-bottom: 15px;
    color: #ffffff;
    text-transform: uppercase;
  }
  
  /* Quick start guide */
  #quick-start-guide {
    margin-bottom: 20px;
    padding: 12px;
    background: #2c2c2c;
    border-radius: 8px;
    text-align: middle;
    color: #e0e0e0;
    font-size: 14px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  }
  #quick-start-guide p {
    margin: 0 0 5px;
    font-weight: 600;
  }
  #quick-start-guide ul {
    padding-left: 20px;
    margin: 0;
    list-style-type: disc;
  }
  
  /* Question text */
  .question {
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 15px;
    color: #fc3b3b;
  }
  
  /* Choice buttons */
  .choices {
    width: 100%;
  }
  .choices button {
    display: block;
    width: 100%;
    margin: 8px 0;
    padding: 12px;
    font-size: 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    background-color: #333333;
    color: #ffffff;
    transition: background-color 0.2s ease;
  }
  .choices button:hover {
    background-color: #444444;
  }
  
  /* Correct and wrong answer styles */
  .correct {
    background-color: #00cc00 !important;
    color: white;
  }
  .wrong {
    background-color: #ff3333 !important;
    color: white;
  }
  
  /* Select and button styling */
  select, button {
    margin: 10px 0;
    padding: 8px;
    font-size: 16px;
    border-radius: 8px;
  }
  
  /* Hidden class */
  .hidden {
    display: none;
  }
  
  /* Quit button */
  #quitButton {
    position: relative;
    top: -10px;
    left: -125px;
    background: linear-gradient(270deg, #232325, #141414);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
  }
  
  /* Info button */
  #infoButton {
    position: relative;
    top: -10px;
    right: -125px;
    background: linear-gradient(270deg, #232325, #141414);
    color: white;
    padding: 8px 12px;
    border-radius: 10px;
  }
  
  /* Skip button */
  #skipButton {
    margin-top: 10px;
    background: #ff9800;
    color: white;
    padding: 12px;
    border-radius: 8px;
  }
  
  /* End Quiz Early button - hidden by default */
  #endEarlyButton {
    margin-top: 10px;
    background: #cc0000;
    color: white;
    padding: 12px;
    border-radius: 8px;
    display: none;
  }
  
  /* Speed Run container styling */
  #speedRunContainer {
    margin-top: 10px;
  }
  #speedRunContainer label {
    margin-right: 10px;
  }
  
  /* Reveal Answer container styling */
  #revealAnswerContainer {
    margin-top: 10px;
  }
  #revealAnswerContainer label {
    margin-right: 10px;
  }
  
  /* Progress display */
  #progress {
    margin-top: 15px;
    font-weight: bold;
    color: #e0e0e0;
  }
  
  /* Feedback display */
  .feedback {
    margin-top: 10px;
    font-size: 22px;
    font-weight: 500;
    color: #ffffff57;
  }
  
  /* Credits button */
  #creditsButton {
    position: absolute;
    top: 405px;
    left: 6px;
    background: transparent;
    color: transparent;
    border: none;
    padding: 15px;
    cursor: pointer;
    opacity: 0;
  }
  
  /* Credits message */
  #creditsMessage {
    display: toggle;
    margin-top: 10px;
    padding: 12px;
    background: #2c2c2c;
    border-radius: 8px;
    color: #e0e0e0;
    font-size: 14px;
    text-align: center;
  }
  
  /* Answer display */
  .answer {
    margin-top: 10px;
    font-size: 32px;
    font-weight: 500;
    color: #fbff00;
    text-shadow: 0 0 2px rgba(255, 67, 67, 0.158);
    text-align: center;
  }
  
  /* Navigation buttons */
  #navigation {
    margin-top: 15px;
    display: hidden;
    justify-content: space-between;
    width: 100%;
  }
  #prevButton, #nextButton {
    background: linear-gradient(270deg, #232325, #141414);
    color: #ffffff;
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease, background 0.2s ease;
  }
  #prevButton:hover, #nextButton:hover {
    transform: scale(1.05);
    background: linear-gradient(90deg, #232325, #141414);
  }
  
  /* Quote display */
  .quote-display {
    margin-top: 15px;
    padding: 15px;
    background: rgba(29, 29, 29, 0.95);
    border-radius: 10px;
    color: #ffffff;
    font-size: 16px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    position: relative;
  }
  #quoteText {
    margin-bottom: 10px;
  }
  #closeQuote {
    background: linear-gradient(270deg, #232325, #141414);
    color: white;
    padding: 5px 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: transform 0.2s ease;
  }
  #closeQuote:hover {
    transform: scale(1.05);
  }
  
  /* Last updated container */
  .last-updated-container {
    margin-top: 5px;
    margin-bottom: 21px;
    color: #e0e0e0;
    font-size: 14px;
  }
  #last-updated {
    display: inline;
  }

  /* Chart screen styling */
  #chart-screen {
    text-align: center;
  }

  #accuracyChart {
    width: 100%;
    height: 400px;
  }